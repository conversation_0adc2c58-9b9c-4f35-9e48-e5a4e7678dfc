<?php
/**
 * User: z<PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * Date: 2020/3/16
 * Time: 17:23
 * wiki: http://wiki.zuoyebang.cc/pages/viewpage.action?pageId=83313743
 */
class Assistant_Dao_Es_IDLAssistantLessonStudentAction extends Assistant_Common_Db_EsBaseDao{
    public function __construct() {
        $this->index = "c";
        $this->arrFieldsMap = array(
            "allLessonIndex"      => "all_lesson_index",
            "assistantUid"        => "assistant_uid",
            'photoAssistantUid'   => 'photo_assistant_uid',
            "attend"              => "attend",
            "attendDuration"      => "attend_duration",
            "preclassAttendDuration" => "preclass_attend_duration",//学员在班主任课前直播累积到课时长
            "isPreclassAttend" => "is_preclass_attend",//课前直播是否到课达标
            "isPreclassFinishAttend" => "is_preclass_finish_attend",//课前直播是否完课
            "postclassAttendDuration" => "postclass_attend_duration",//学员在班主任课后直播累积到课时长
            "isPostclassAttend" => "is_postclass_attend",//课后直播是否到课达标
            "isPostclassFinishAttend" => "is_postclass_finish_attend",//课后直播是否完课
            "isPlaybackLongAfterUnlock7d"   => "is_playback_long_after_unlock_7d",//录播是否7天内观看
            "isPlaybackFinishAfterUnlock7d" => "is_playback_finish_after_unlock_7d",//录播是否7天内完成观看
            "playbackTimeAfterUnlock"       => "playback_time_after_unlock",//录播累计录播观看时长


            "attendLong"          => "attend_long",//是否到课30min
            "isAttendFinish"      => "is_attend_finish",//是否完课，上课占比0.75
            "boostLessonIndex"    => "boost_lesson_index",
            "chatNum"             => "chat_num",
            "courseId"            => "course_id",
            "courseName"          => "course_name",
            "courseStatus"        => "course_status",
            "courseType"          => "course_type",
            "department"          => "department",
            "examAnswer"          => "exam_answer",
            "grades"              => "grades",
            "isNeedAttend"        => "is_need_attend",
            "learnSeason"         => "learn_season",
            "lessonDeleted"       => "lesson_deleted",
            "lessonFinishTime"    => "lesson_finish_time",
            "lessonId"            => "lesson_id",
            "lessonName"          => "lesson_name",
            "lessonStartTime"     => "lesson_start_time",
            "lessonStatus"        => "lesson_status",
            "lessonStopTime"      => "lesson_stop_time",
            "lessonType"          => "lesson_type",
//            "lessonScore"         => "lesson_score", //2021年12月31日 下线
            "mainDepartment"      => "main_department",
            "mainGrade"           => "main_grade",
            "mainLessonIndex"     => "main_lesson_index",
            "mainSubject"         => "main_subject",
            "micNum"              => "mic_num",
            "newUserType"         => "new_user_type",
            "playbackTimeIn7d"    => "playback_time_in_7d",
            "playbackTotalTime"   => "playback_time",
//            "praiseNum"           => "praise_num", //2021年12月31日 下线
            "preAttend"           => "pre_attend",
            "restartId"           => "restart_id",
            "seasonIndex"         => "season_index",
            "studentUid"          => "student_uid",
            "subTradeId"          => "sub_trade_id",
            "subjects"            => "subjects",
            "teacherId"           => "teacher_id",
            "tradeChangeStatus"   => "trade_change_status",
            "tradeChangeTime"     => "trade_change_time",
            "tradeCreateTime"     => "trade_create_time",
            "tradeRefundTime"     => "trade_refund_time",
            "tradeStatus"         => "trade_status",
            "updateTime"          => "update_time",
            "year"                => "year",

            // 上课表现 http://wiki.zuoyebang.cc/pages/viewpage.action?pageId=113686815
            'attendOverview'          => 'attend_detail', // 课中行为概况
            'lessonRealStartTime'     => 'lesson_real_start_time', // 章节实际开课时间
            'interactionAnswerDetail' => 'interaction_answer_detail', // 口述题行为明细
            'inoutCount'              => 'inout_count', // 课中进出教室次数
            'attendLabel'             => 'student_attend_label', // 课中行为标签
            'interactionLabel'        => 'student_interaction_label', // 口述题行为标签

            //互动题
            'isExerciseSubmit'                  => 'exam_answer.exam1.is_submit',
            "exerciseTotalNum"                  => "exam_answer.exam1.total_num", // 字段已下线，请转移至索引 bdl_exam_relation 中查询【后续删除此字段配置】
            "exerciseRightNum"                  => "exam_answer.exam1.right_num",
            "exerciseParticipateNum"            => "exam_answer.exam1.participate_num",
            "playbackAllNum"                    => "playback_all_num",
            "playbackParticipateNum"            => "playback_participate_num",
            "playbackRightNum"                  => "playback_right_num",


            //课前预习
            "isPreviewFinish5"       => "exam_answer.exam5.is_finish",
            "previewFinishTime5"     => "exam_answer.exam5.finish_time",
            "previewParticipateNum5" => "exam_answer.exam5.participate_num",
            "previewCorrectNum5"     => "exam_answer.exam5.right_num",
            "previewTotalNum5"       => "exam_answer.exam5.total_num", // 字段已下线，请转移至索引 bdl_exam_relation 中查询【后续删除此字段配置】
            "previewIsExpound5"      => "exam_answer.exam5.is_expound",

            //巩固练习
            "isHomeworkSubmit"               => "exam_answer.exam7.is_submit",
            "homeworkFinishTime"             => "exam_answer.exam7.submit_time",
            "homeworkLastSubmitTime"         => "exam_answer.exam7.last_submit_time",
            "homeworkPracticeParticipateNum" => "exam_answer.exam7.participate_num",
            "homeworkPracticeCorrectNum"     => "exam_answer.exam7.right_num",
            "homeworkPracticeTotalNum"       => "exam_answer.exam7.total_num", // 字段已下线，请转移至索引 bdl_exam_relation 中查询【后续删除此字段配置】
            "homeworkLevel"                  => "exam_answer.exam7.correct_level",
            "homeworkLastCorrectTime"        => "exam_answer.exam7.last_correct_time",
            "homeworkSubmissions"            => "exam_answer.exam7.submit_num",
            "homeworkIsExpound"              => "exam_answer.exam7.is_expound",
            "homeworkIsAmend"                => "exam_answer.exam7.is_amend",
            'isViewHomeworkExpoundVideo'     => 'exam_answer.exam7.is_view_wrong_expound_video', // 是否观看原版错题视频 1已看|0 未看

            //ilab巩固练习个性必答题目总数
            "ilabHomeworkGeXingTotalNum"     => "exam_answer.exam31.must_gexing_total_num",//必做卷个性题总数 // 字段已下线，请转移至索引 bdl_exam_relation 中查询【后续删除此字段配置】
            "ilabHomeworkMustRightNum"       => "exam_answer.exam31.must_right_num",//必做卷正确数
            "ilabHomeworkMustIsSubmit"       => "exam_answer.exam31.must_is_submit",//必做卷是否提交
            "ilabHomeworkMustSubmitTime"       => "exam_answer.exam31.must_submit_time",//必做卷提交时间
            "ilabHomeworkMustParticipateNum"   => "exam_answer.exam31.must_participate_num",//必做卷参与数


            //阶段测
            "isStageTestExamSubmit"       => "exam_answer.exam9.is_submit",
            "stageTestExamFinishTime"     => "exam_answer.exam9.submit_time",
            "stageTestExamParticipateNum" => "exam_answer.exam9.participate_num",
            "stageTestExamCorrectNum"     => "exam_answer.exam9.right_num",
            "stageTestExamTotalNum"       => "exam_answer.exam9.total_num", // 字段已下线，请转移至索引 bdl_exam_relation 中查询【后续删除此字段配置】
            "stageTestIsExpound"          => "exam_answer.exam9.is_expound",
            "stageTestIsCorrect"          => "exam_answer.exam9.is_correct",
            "stageTestCorrectLevel"       => "exam_answer.exam9.correct_level",
            "stageTestCorrectTime"        => "exam_answer.exam9.correct_time",

            //堂堂测
            "isTangTangExamSubmit"       => "exam_answer.exam10.is_submit",
            "tangTangExamFinishTime"     => "exam_answer.exam10.submit_time",
            "tangTangExamParticipateNum" => "exam_answer.exam10.participate_num",
            "tangTangExamCorrectNum"     => "exam_answer.exam10.right_num",
            "tangTangExamTotalNum"       => "exam_answer.exam10.total_num", // 字段已下线，请转移至索引 bdl_exam_relation 中查询【后续删除此字段配置】
            "tangTangIsExpound"          => "exam_answer.exam10.is_expound",
            'tangTangExamScore'          => 'exam_answer.exam10.answer_score',

            //同步练习
            "isSynchronousPracticeSubmit"       => "exam_answer.exam11.is_submit",
            "synchronousPracticeParticipateNum" => "exam_answer.exam11.participate_num",
            "synchronousPracticeCorrectNum"     => "exam_answer.exam11.right_num",
            "synchronousPracticeTotalNum"       => "exam_answer.exam11.total_num", // 字段已下线，请转移至索引 bdl_exam_relation 中查询【后续删除此字段配置】

            //初高中预习
            "previewIsSubmit13"       => "exam_answer.exam13.is_submit",
            "isPreviewFinish13"       => "exam_answer.exam13.is_finish",
            "previewFinishTime13"     => "exam_answer.exam13.finish_time",
            "previewParticipateNum13" => "exam_answer.exam13.participate_num",
            "previewCorrectNum13"     => "exam_answer.exam13.right_num",
            "previewTotalNum13"       => "exam_answer.exam13.total_num", // 字段已下线，请转移至索引 bdl_exam_relation 中查询【后续删除此字段配置】

            // 口述题
            //'haveOralQuestion'           => 'exam_answer.exam32.is_have', // 字段已下线，请转移至索引 bdl_exam_relation 中查询 是否有口述题
            'oralQuestionTotalNum'       => 'exam_answer.exam32.total_num', // 口述题题目总数 // 字段已下线，请转移至索引 bdl_exam_relation 中查询【后续删除此字段配置】
            'oralQuestionSubmit'         => 'exam_answer.exam32.is_submit', // 口述题是否提交
            'oralQuestionParticipateNum' => 'exam_answer.exam32.participate_num', // 口述题参与题目数
            'oralQuestionCorrectNum'     => 'exam_answer.exam32.right_num', // 口述题答对题目数
            'oralQuestionSubmitTime'     => 'exam_answer.exam32.submit_time', // 口述题提交时间
            'oralQuestionCorrectTime'    => 'exam_answer.exam32.correct_time', // 口述题批改时间

            //内部阶段测
            "innerStageIsSubmit"         => "exam_answer.exam34.is_submit",
            "innerStageHighestScore"     => "exam_answer.exam34.highest_score",
            "innerStageIsPass"           => "exam_answer.exam34.is_pass",
            "innerStageIsFullScore"      => "exam_answer.exam34.is_full_score",
            "innerStageAnswerDetail"     => "exam_answer.exam34.exam_answer_detail",

            /* --------- 帮帮英语 start -------- */
            //单词练习
            "wordpracticeParticipateNum" => "exam_answer.exam14.participate_num",
            "wordpracticeRightNum"       => "exam_answer.exam14.right_num",
            "wordpracticeTotalNum"       => "exam_answer.exam14.total_num", // 字段已下线，请转移至索引 bdl_exam_relation 中查询【后续删除此字段配置】

            //单词学习
            "wordlearnParticipateNum" => "exam_answer.exam15.participate_num",
            "wordlearnRightNum"       => "exam_answer.exam15.right_num",
            "wordlearnTotalNum"       => "exam_answer.exam15.total_num", // 字段已下线，请转移至索引 bdl_exam_relation 中查询【后续删除此字段配置】

            //出门测
            "outTestParticipateNum" => "exam_answer.exam18.participate_num",
            "outTestRightNum"       => "exam_answer.exam18.right_num",
            "outTestTotalNum"       => "exam_answer.exam18.total_num", // 字段已下线，请转移至索引 bdl_exam_relation 中查询【后续删除此字段配置】

            //提升训练
            "improveParticipateNum" => "exam_answer.exam19.participate_num",
            "improveRightNum"       => "exam_answer.exam19.right_num",
            "improveTotalNum"       => "exam_answer.exam19.total_num", // 字段已下线，请转移至索引 bdl_exam_relation 中查询【后续删除此字段配置】

            //语法练习
            "grammarpracticeParticipateNum" => "exam_answer.exam21.participate_num",
            "grammarpracticeRightNum"       => "exam_answer.exam21.right_num",
            "grammarpracticeTotalNum"       => "exam_answer.exam21.total_num", // 字段已下线，请转移至索引 bdl_exam_relation 中查询【后续删除此字段配置】

            //练一练
            "practicesParticipateNum" => "exam_answer.exam17.participate_num",
            "practicesRightNum"       => "exam_answer.exam17.right_num",
            "practicesTotalNum"       => "exam_answer.exam17.total_num", // 字段已下线，请转移至索引 bdl_exam_relation 中查询【后续删除此字段配置】

            //入门测
            "inTestParticipateNum" => "exam_answer.exam23.participate_num",
            "inTestRightNum"       => "exam_answer.exam23.right_num",
            "inTestTotalNum"       => "exam_answer.exam23.total_num", // 字段已下线，请转移至索引 bdl_exam_relation 中查询【后续删除此字段配置】

            /* --------- 帮帮英语 end -------- */

            /* --------- 浣熊英语 start -------- */

            //小试牛刀
            "tryKnifeScore"        => "exam_answer.exam211.score",
            "tryKnifeSubmitTime"   => "exam_answer.exam211.submit_time",
            "tryKnifeSubmitStatus" => "exam_answer.exam211.submit_status",

            //lesson1
            "lessonOneScore"        => "exam_answer.exam212.lesson1_score",
            "lessonOneSubmitTime"   => "exam_answer.exam212.lesson1_submit_time",
            "lessonOneSubmitStatus" => "exam_answer.exam212.lesson1_submit_status",

            //lesson2
            "lessonTwoScore"        => "exam_answer.exam212.lesson2_score",
            "lessonTwoSubmitTime"   => "exam_answer.exam212.lesson2_submit_time",
            "lessonTwoSubmitStatus" => "exam_answer.exam212.lesson2_submit_status",

            //lesson3
            "lessonThreeScore"        => "exam_answer.exam212.lesson3_score",
            "lessonThreeSubmitTime"   => "exam_answer.exam212.lesson3_submit_time",
            "lessonThreeSubmitStatus" => "exam_answer.exam212.lesson3_submit_status",

            //综合演练
            "synthesisManoeuvreScore"        => "exam_answer.exam216.score",
            "synthesisManoeuvreSubmitTime"   => "exam_answer.exam216.submit_time",
            "synthesisManoeuvreSubmitStatus" => "exam_answer.exam216.submit_status",

            //能力挑战
            "powerChallengeScore"        => "exam_answer.exam213.score",
            "powerChallengeSubmitTime"   => "exam_answer.exam213.submit_time",
            "powerChallengeSubmitStatus" => "exam_answer.exam213.submit_status",

            //综合秀场
            "synthesisShowScore"        => "exam_answer.exam217.score",
            "synthesisShowSubmitTime"   => "exam_answer.exam217.submit_time",
            "synthesisShowSubmitStatus" => "exam_answer.exam217.submit_status",

            //浣熊课前测试题的完成数量
            "hxPretestFinishnum" => "hx_pretest_finishnum",

            //浣熊全部测试题的完成数量
            "hxAlltestAinishnum" => "hx_alltest_finishnum",

            /* --------- 浣熊英语 end -------- */

            "attendQuarter" => "attend_quarter",        //   专题课   是否到课四分之一
            "isPlaybackQuarter" => "is_playback_quarter",        //      专题课  是否回放四分之一

            //录播
            "isPlaybackLongAfterUnlock7d"      => "is_playback_long_after_unlock_7d", //解锁后7天内观看（1 完成，0 未完成）
            'isPlaybackFinishAfterUnlock7d'    => 'is_playback_finish_after_unlock_7d', //解锁后7天完课回放完成(1 完成,0 未完成)
            'lessonUnlockTime'                 => 'lesson_unlock_time',//解锁时间
            'playbackTimeAfterUnlock'          => 'playback_time_after_unlock', //解锁后回放时长
            'playbackTimeAfterUnlock7d'        => 'playback_time_after_unlock_7d', //解锁后7天内回放时长
            'isPlaybackLongAfterUnlock'        => 'is_playback_long_after_unlock',  //解锁后到课回放完成 （1 完成，0 未完成）
            'isPlaybackFinishAfterUnlock'      => 'is_playback_finish_after_unlock', //解锁后完课回放完成 （1 完成，0 未完成）
            'isNeedAttendAfterUnlock'          => 'is_need_attend_after_unlock',//解锁后是否应到课 （1是 0否）
            'isViewFinished'                   => 'is_view_finished',//到课完课状态
            'isViewFinishIn14d'                => 'is_view_finish_in_14d', //初中到课完课状态
            'playbackTimeIn14d'                => 'playback_time_in_14d', //14天内回放时长
            //小灶课
            'isAssistantcourseAttend'       => 'is_assistantcourse_attend',//小灶课到课状态（>1/3）
            'isAssistantcourseFinish'       => 'is_assistantcourse_finish',//小灶课完课状态（>2/3)
            //LBP
            'isLbpAttend'                   => 'is_lbp_attend', //LBP到课
            'isLbpAttendFinish'             => 'is_lbp_attend_finish', //LBP完课
            'lbpAttendDuration'             => 'lbp_attend_duration', //LBP观看时长
            'lbpLastPlaytime'               => 'lbp_last_playtime', //LBP最后一次观看时间
            'lbpInteractionRightNum'        => 'lbp_interaction_right_num', //LBP课中互动题正确数
            'lbpInteractionTotalNum'        => 'lbp_interaction_total_num', //LBP课中互动题总题数
            'lbpInteractionSubmitNum'       => 'lbp_interaction_submit_num', //LBP课中互动题提交数
            'lbpOnlineDuration'             => 'lbp_online_duration', //LBP累积在线时长
            'lbpPlayDetail'                 => 'lbp_play_detail', //学生进出直播间明细
            'inclassTeacherRoomTotalPlaybackTimeV1'=> 'inclass_teacher_room_total_playback_time_v1', //回放时长(新)
        );
        $this->arrTypesMap = array(
            "allLessonIndex"            => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "assistantUid"              => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "photoAssistantUid"         => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "attend"                    => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "attendDuration"            => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "preclassAttendDuration"    => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "postclassAttendDuration"   => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "attendLong"                => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "isAttendFinish"            => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "boostLessonIndex"    => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "chatNum"             => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "courseId"            => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "courseName"          => Assistant_Common_Db_EsBaseDao::TYPE_STR,
            "courseStatus"        => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "courseType"          => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "department"          => Assistant_Common_Db_EsBaseDao::TYPE_STR,
            "examAnswer"          => Assistant_Common_Db_EsBaseDao::TYPE_JSON,
            "grades"              => Assistant_Common_Db_EsBaseDao::TYPE_STR,
            "isNeedAttend"        => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "learnSeason"         => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "lessonDeleted"       => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "lessonFinishTime"    => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "lessonId"            => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "lessonName"          => Assistant_Common_Db_EsBaseDao::TYPE_STR,
            "lessonStartTime"     => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "lessonStatus"        => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "lessonStopTime"      => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "lessonType"          => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "mainDepartment"      => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "mainGrade"           => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "mainLessonIndex"     => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "mainSubject"         => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "micNum"              => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "newUserType"         => Assistant_Common_Db_EsBaseDao::TYPE_STR,
            "playbackTimeIn7d"    => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "playbackTotalTime"   => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "preAttend"           => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "restartId"           => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "seasonIndex"         => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "studentUid"          => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "subTradeId"          => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "subjects"            => Assistant_Common_Db_EsBaseDao::TYPE_STR,
            "teacherId"           => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "tradeChangeStatus"   => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "tradeChangeTime"     => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "tradeCreateTime"     => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "tradeRefundTime"     => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "tradeStatus"         => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "updateTime"          => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "year"                => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "isPlaybackLongAfterUnlock7d"   => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "isPlaybackFinishAfterUnlock7d" => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "playbackTimeAfterUnlock"       => Assistant_Common_Db_EsBaseDao::TYPE_INT,

            // 上课表现
            'attendOverview'          => Assistant_Common_Db_EsBaseDao::TYPE_JSON,
            'lessonRealStartTime'     => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            'interactionAnswerDetail' => Assistant_Common_Db_EsBaseDao::TYPE_JSON,
            'inoutCount'              => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            'attendLabel'             => Assistant_Common_Db_EsBaseDao::TYPE_JSON,
            'interactionLabel'        => Assistant_Common_Db_EsBaseDao::TYPE_JSON,

            //互动题
            'isExerciseSubmit'                  => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "exerciseTotalNum"                  => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "exerciseRightNum"                  => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "exerciseParticipateNum"            => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "playbackAllNum"                    => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "playbackParticipateNum"            => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "playbackRightNum"                  => Assistant_Common_Db_EsBaseDao::TYPE_INT,

            //课前预习
            "isPreviewFinish5"       => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "previewFinishTime5"     => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "previewParticipateNum5" => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "previewCorrectNum5"     => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "previewTotalNum5"       => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "previewIsExpound5"      => Assistant_Common_Db_EsBaseDao::TYPE_INT,

            //巩固练习
            "isHomeworkSubmit"               => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "homeworkFinishTime"             => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "homeworkLastSubmitTime"         => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "homeworkPracticeParticipateNum" => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "homeworkPracticeCorrectNum"     => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "homeworkPracticeTotalNum"       => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "homeworkLevel"                  => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "homeworkLastCorrectTime"        => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "homeworkSubmissions"            => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "homeworkIsExpound"              => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "homeworkIsAmend"                => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            'isViewHomeworkExpoundVideo'     => Assistant_Common_Db_EsBaseDao::TYPE_INT,

            //ilab巩固练习个性必答题目总数
            "ilabHomeworkGeXingTotalNum"     => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "ilabHomeworkMustRightNum"       => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "ilabHomeworkMustIsSubmit"       => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "ilabHomeworkMustSubmitTime"     => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "ilabHomeworkMustParticipateNum" => Assistant_Common_Db_EsBaseDao::TYPE_INT,

            //阶段测
            "isStageTestExamSubmit"       => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "stageTestExamFinishTime"     => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "stageTestExamParticipateNum" => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "stageTestExamCorrectNum"     => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "stageTestExamTotalNum"       => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "stageTestIsExpound"          => Assistant_Common_Db_EsBaseDao::TYPE_INT,

            //堂堂测
            "isTangTangExamSubmit"       => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "tangTangExamFinishTime"     => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "tangTangExamParticipateNum" => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "tangTangExamCorrectNum"     => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "tangTangExamTotalNum"       => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "tangTangIsExpound"          => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            'tangTangExamScore'          => Assistant_Common_Db_EsBaseDao::TYPE_INT,

            //同步练习
            "isSynchronousPracticeSubmit"       => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "synchronousPracticeParticipateNum" => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "synchronousPracticeCorrectNum"     => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "synchronousPracticeTotalNum"       => Assistant_Common_Db_EsBaseDao::TYPE_INT,

            //初高中预习
            "previewIsSubmit13" => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "isPreviewFinish13"   => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "previewFinishTime13" => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "previewParticipateNum13" => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "previewCorrectNum13" => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "previewTotalNum13"   => Assistant_Common_Db_EsBaseDao::TYPE_INT,

            // 口述题
            //'haveOralQuestion'           => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            'oralQuestionTotalNum'       => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            'oralQuestionSubmit'         => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            'oralQuestionParticipateNum' => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            'oralQuestionCorrectNum'     => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            'oralQuestionSubmitTime'     => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            'oralQuestionCorrectTime'    => Assistant_Common_Db_EsBaseDao::TYPE_INT,

            //单词练习
            "wordpracticeParticipateNum" => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "wordpracticeRightNum"       => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "wordpracticeTotalNum"       => Assistant_Common_Db_EsBaseDao::TYPE_INT,

            //单词学习
            "wordlearnParticipateNum" => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "wordlearnRightNum"       => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "wordlearnTotalNum"       => Assistant_Common_Db_EsBaseDao::TYPE_INT,

            //出门测
            "outTestParticipateNum" => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "outTestRightNum"       => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "outTestTotalNum"       => Assistant_Common_Db_EsBaseDao::TYPE_INT,

            //提升训练
            "improveParticipateNum" => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "improveRightNum"       => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "improveTotalNum"       => Assistant_Common_Db_EsBaseDao::TYPE_INT,

            //语法练习
            "grammarpracticeParticipateNum" => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "grammarpracticeRightNum"       => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "grammarpracticeTotalNum"       => Assistant_Common_Db_EsBaseDao::TYPE_INT,

            //练一练
            "practicesParticipateNum" => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "practicesRightNum"       => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "practicesTotalNum"       => Assistant_Common_Db_EsBaseDao::TYPE_INT,

            //入门测
            "inTestParticipateNum" => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "inTestRightNum"       => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "inTestTotalNum"       => Assistant_Common_Db_EsBaseDao::TYPE_INT,


            //小试牛刀
            "tryKnifeScore"        => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "tryKnifeSubmitTime"   => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "tryKnifeSubmitStatus" => Assistant_Common_Db_EsBaseDao::TYPE_INT,

            //lesson1
            "lessonOneScore"        => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "lessonOneSubmitTime"   => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "lessonOneSubmitStatus" => Assistant_Common_Db_EsBaseDao::TYPE_INT,

            //lesson2
            "lessonTwoScore"        => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "lessonTwoSubmitTime"   => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "lessonTwoSubmitStatus" => Assistant_Common_Db_EsBaseDao::TYPE_INT,

            //lesson3
            "lessonThreeScore"        => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "lessonThreeSubmitTime"   => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "lessonThreeSubmitStatus" => Assistant_Common_Db_EsBaseDao::TYPE_INT,

            //综合演练
            "synthesisManoeuvreScore"        => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "synthesisManoeuvreSubmitTime"   => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "synthesisManoeuvreSubmitStatus" => Assistant_Common_Db_EsBaseDao::TYPE_INT,

            //能力挑战
            "powerChallengeScore"        => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "powerChallengeSubmitTime"   => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "powerChallengeSubmitStatus" => Assistant_Common_Db_EsBaseDao::TYPE_INT,

            //综合秀场
            "synthesisShowScore"        => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "synthesisShowSubmitTime"   => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "synthesisShowSubmitStatus" => Assistant_Common_Db_EsBaseDao::TYPE_INT,

            //浣熊课前测试题的完成数量
            "hxPretestFinishnum" => Assistant_Common_Db_EsBaseDao::TYPE_INT,

            //浣熊全部测试题的完成数量
            "hxAlltestAinishnum" => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "attendQuarter"      => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "isPlaybackQuarter"  => Assistant_Common_Db_EsBaseDao::TYPE_INT,

            //录播
            "isPlaybackLongAfterUnlock7d"            => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "isPlaybackFinishAfterUnlock7d"          => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "lessonUnlockTime"                       => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "playbackTimeAfterUnlock"                => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "playbackTimeAfterUnlock7d"              => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "isPlaybackLongAfterUnlock"              => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "isPlaybackFinishAfterUnlock"            => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            "isNeedAttendAfterUnlock"                => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            'isViewFinished'                         => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            'isViewFinishIn14d'                      => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            'playbackTimeIn14d'                      => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            //小灶课
            'isAssistantcourseAttend'           => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            'isAssistantcourseFinish'           => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            //LBP
            'isLbpAttend'                   => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            'isLbpAttendFinish'             => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            'lbpAttendDuration'             => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            'lbpLastPlaytime'               => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            'lbpInteractionRightNum'        => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            'lbpInteractionTotalNum'        => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            'lbpInteractionSubmitNum'       => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            'lbpOnlineDuration'             => Assistant_Common_Db_EsBaseDao::TYPE_INT,
            'lbpPlayDetail'                 => Assistant_Common_Db_EsBaseDao::TYPE_JSON,
            'inclassTeacherRoomTotalPlaybackTimeV1' => Assistant_Common_Db_EsBaseDao::TYPE_INT,
        );
    }
}
