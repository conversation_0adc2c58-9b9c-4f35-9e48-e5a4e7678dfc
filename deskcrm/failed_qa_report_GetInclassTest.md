# QA验证失败报告 - GetInclassTest

## 函数名 (FuncName)
GetInclassTest

## 问题描述 (Description)
Go 实现中缺少小英课程（浣熊课程）的判断逻辑和特殊处理，导致与 PHP 版本的输出不一致。具体问题包括：

1. **小英课程判断逻辑缺失**：Go 代码中 `checkIsHx` 方法未实现，`isHxCourse` 硬编码为 `false`
2. **小英课程得分显示缺失**：缺少小英课程的得分格式化逻辑（"X分" 或 "未提交"）
3. **iLab 课程特殊处理缺失**：缺少 iLab 课程的等级映射和颜色标识逻辑

这将导致小英课程的堂堂测数据显示格式与 PHP 版本完全不同，影响业务功能的一致性。

## 复现路径/代码片段 (Reproduce Steps/Code Snippet)

### PHP Snippet (正确逻辑)
```php
// 小英课程判断
$isHxCourse = Common_Singleton::getInstanceData(AssistantDesk_Tools::class, "checkIsHx", [AssistantDesk_Data_CommonParams::$courseId]);

if(!$isHxCourse){
    // 普通课程：显示 "X|Y|Z" 格式
    self::$student['inclassTest'] = sprintf(
        self::LESSON_EXERCISE_DETAIL,
        $esLessonData[self::$studentUid]['tangTangExamCorrectNum'],
        $esLessonData[self::$studentUid]['tangTangExamParticipateNum'],
        $tangTangToalNum
    );
}else{
    // 小英课程：展示得分
    self::$student['inclassTest'] = $esLessonData[self::$studentUid]['isTangTangExamSubmit']
        ? sprintf('%s分', ($esLessonData[self::$studentUid]['tangTangExamScore'] / 10))
        : '未提交';
}

// iLab 课程特殊处理
if ($gradeId == 3 && $subjectId == 4 && $this->checkIlabLesson[$lessonId]['iLabLesson']) {
    $row['inclassTest'][0] = Api_ExamUI::$levelIlabMap[intval($this->inclassTestInfoByIlab[$lessonId])] ? Api_ExamUI::$levelIlabMap[intval($this->inclassTestInfoByIlab[$lessonId])] : "-";
    if ($this->inclassTestInfoByIlab[$lessonId] == 1) {
        $row['inclassTest'][1] = 'green';//优秀
    }
    if ($this->inclassTestInfoByIlab[$lessonId] == 2) {
        $row['inclassTest'][1] = 'orange';//良好
    }
}
```

### Go Snippet (有问题的逻辑)
```go
// 检查是否是小英课程
isHxCourse := false
// TODO: 需要实现checkIsHx方法，这里暂时使用默认值
// isHxCourse = checkIsHx(s.param.CourseID)

for _, lessonID := range s.param.LessonIDs {
    // 根据课程类型显示不同格式
    if !isHxCourse {
        // 非小英课程：显示 "X|Y|Z" 格式
        if tangTangTotalNum > 0 {
            inclassTestArray[0] = fmt.Sprintf(dataQuery.LESSON_EXERCISE_DETAIL, correctNum, participateNum, tangTangTotalNum)
            // 设置颜色：满分绿色，不满分橙色
            if correctNum == tangTangTotalNum {
                inclassTestArray[1] = "green" // 满分
            } else if correctNum < tangTangTotalNum {
                inclassTestArray[1] = "orange" // 不满分
            }
        } else {
            inclassTestArray[0] = "-"
        }
    } else {
        // 小英课程：展示得分，对应PHP的sprintf('%s分', ($esLessonData[self::$studentUid]['tangTangExamScore'] / 10))
        if isSubmit {
            inclassTestArray[0] = fmt.Sprintf("%d分", score/10)
        } else {
            inclassTestArray[0] = "未提交"
        }
    }
    // 注意：缺少 iLab 课程的特殊处理逻辑
}
```

## 建议修复方案 (Suggested Fix)

1. **实现 checkIsHx 方法**：
   - 参考 PHP 中 `AssistantDesk_Tools::checkIsHx` 的实现
   - 根据课程的 serviceInfo 判断是否为小英课程
   - 检查 serviceId 是否包含小英直播间的服务ID

2. **添加小英课程得分显示逻辑**：
   - 当 `isHxCourse = true` 时，使用得分格式化逻辑
   - 检查 `isTangTangExamSubmit` 字段决定显示得分还是"未提交"
   - 得分需要除以10进行格式化

3. **添加 iLab 课程特殊处理**：
   - 实现年级和学科的判断逻辑（gradeId == 3 && subjectId == 4）
   - 添加 iLab 课程检查逻辑
   - 实现等级映射和颜色标识逻辑

4. **数据源确认**：
   - 确保 Go 代码能够获取到 `tangTangExamScore` 和 `isTangTangExamSubmit` 字段
   - 确保能够获取到课程的年级和学科信息用于 iLab 判断

## 影响范围
- 所有小英课程的堂堂测数据显示将与 PHP 版本不一致
- iLab 课程的特殊显示逻辑完全缺失
- 可能影响前端展示和用户体验的一致性
