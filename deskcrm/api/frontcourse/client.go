package frontcourse

import (
	"deskcrm/api"
	"deskcrm/conf"
	"deskcrm/libs/utils"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// Client frontcourse API客户端
type Client struct {
	cli *base.ApiClient
}

// NewClient create Client instance
func NewClient() *Client {
	c := &Client{
		cli: conf.API.FrontCourse,
	}
	return c
}

const (
	getLessonPreviewInfoApi = "/frontcourse/product/exam/lessonpreviewinfo" // 获取初高中预习结果
)

// GetHighGradePreviewInfo 获取初高中预习结果
// 对应PHP中的Api_Jx::getHighGradePreviewInfo方法
func (c *Client) GetHighGradePreviewInfo(ctx *gin.Context, lessonIds []int64) (map[int64]HighGradePreviewInfo, error) {
	if len(lessonIds) == 0 {
		return make(map[int64]HighGradePreviewInfo), nil
	}

	// 构建请求参数
	req := map[string]interface{}{
		"lessonIds": lessonIds,
	}

	// 设置请求选项
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeJson}
	utils.DecorateHttpOptions(ctx, &opts)

	// 发送请求
	res, err := c.cli.HttpPost(ctx, getLessonPreviewInfoApi, opts)
	if err != nil {
		zlog.Warnf(ctx, "FrontCourse GetHighGradePreviewInfo request err: %v", err)
		return nil, err
	}

	// 检查 HTTP 状态码
	if err = api.ApiHttpCode(ctx, res); err != nil {
		return nil, err
	}

	// 解析响应
	var frontCourseResp map[int64]HighGradePreviewInfo
	if _, err = api.DecodeResponse(ctx, res, &frontCourseResp); err != nil {
		return nil, err
	}

	// 转换为map格式
	result := make(map[int64]HighGradePreviewInfo)
	for lessonId, info := range frontCourseResp {
		result[lessonId] = info
	}

	return result, nil
}
