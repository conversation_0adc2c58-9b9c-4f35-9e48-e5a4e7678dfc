package dal

import (
	"deskcrm/util"
	"errors"
	"strconv"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"

	"deskcrm/api"
	"deskcrm/api/zbcore"
	"deskcrm/components"
	"deskcrm/conf"
)

const ApiMaxNum = 50

// 章节播放类型
const (
	PLAY_TYPE_LIVE        = 1 //直播
	PLAY_TYPE_PLAYBACK    = 2 //伪直播
	PLAY_TYPE_AI_INTERACT = 3 //录播(按时解锁)
	PLAY_TYPE_RECORD      = 4 //录播(即买即上)
	PLAY_TYPE_AI_HUDONG   = 5 //AI互动
	PLAY_TYPE_LUBOKE      = 6 //录播 lbp
	PLAY_TYPE_SP_INTERACT = 7 //视频互动课
)

// 章节状态
const (
	LessonStatusUnfinish = 0 //未结束
	LessonStatusFinished = 1 //已结束（正常结束）
	LessonStatusDeleted  = 2 //已删除（异常结束）
)

// 上课状态
const (
	LessonClassingNo  = 0 //章节非上课中
	LessonClassingYes = 1 //章节上课中
)

const (
	LessonTypeMain    = 1 //主体
	LessonTypeAdvance = 2 //提升
)

// 章节重开状态 0正常 1重开 2被重开
const (
	LessonReopenNormal   = 0
	LessonReopenOn       = 1
	LessonReopenReopened = 2
)

const (
	ServiceIdPreClass   = 2397 // 课前出镜
	ServiceIdMidClass   = 2398 // 课中出镜
	ServiceIdAfterClass = 2399 // 课后出镜
)

const (
	LessonModeOfficial  = 1 //正式课
	LessonModeExercises = 2 //习题课
	LessonModeOther     = 3 //其他

)

const (
	LittleEnglishLiveRoomServiceId     = 2473 //浣熊 、lpc统一使用小英直播间
	HalfLittleEnglishLiveRoomServiceId = 2475 //小英半身直播间
	LittleClassLiveRoomServiceId       = 2477 //小班直播间
	EnglishTeacherLiveRoomServiceId    = 2474 //外教直播间
	MixRoomServiceId                   = 3166 //融合直播间
	LbpSitLiveRoomServiceId            = 2482 //LBP小组站播
	LbpStandLiveRoomServiceId          = 2480 //LBP小组坐播
	LbpTripleLiveRoomServiceId         = 2481 //LBP三分屏坐播
)

const (
	LessonModule = "dal"
	LessonEntity = "lesson"
)

type ServiceInfo struct {
	ContainerId     int                    `json:"containerId" mapstructure:"containerId"`
	ContainerType   int                    `json:"containerType" mapstructure:"containerType"`
	ServiceProvider int                    `json:"serviceProvider" mapstructure:"serviceProvider"`
	ServiceId       int                    `json:"serviceId" mapstructure:"serviceId"`
	ServiceName     string                 `json:"serviceName" mapstructure:"serviceName"`
	ServiceStatus   int                    `json:"serviceStatus" mapstructure:"serviceStatus"`
	ExtData         map[string]interface{} `json:"extData" mapstructure:"extData"`
}

type LessonInfo struct {
	LessonId       int           `json:"lessonId" mapstructure:"lessonId"`
	LessonName     string        `json:"lessonName" mapstructure:"lessonName"`
	StartTime      int           `json:"startTime" mapstructure:"startTime"`
	StopTime       int           `json:"stopTime" mapstructure:"stopTime"`
	FinishTime     int           `json:"finishTime" mapstructure:"finishTime"`
	Status         int           `json:"status" mapstructure:"status"`
	OutlineId      int           `json:"outlineId" mapstructure:"outlineId"`
	CourseId       []int         `json:"courseId" mapstructure:"courseId"`
	ShareIDList    []int         `json:"shareIdList" mapstructure:"shareIdList"`
	LessonType     int           `json:"lessonType" mapstructure:"lessonType"`
	HasHomework    int           `json:"hasHomework" mapstructure:"hasHomework"`
	HasPlayback    int           `json:"hasPlayback" mapstructure:"hasPlayback"`
	PreviewNoteUri interface{}   `json:"previewNoteUri" mapstructure:"previewNoteUri"`
	ClassNoteUri   interface{}   `json:"classNoteUri" mapstructure:"classNoteUri"`
	ReopenLessonId int           `json:"reopenLessonId" mapstructure:"reopenLessonId"`
	FileList       interface{}   `json:"fileList" mapstructure:"fileList"`
	ServiceInfo    []ServiceInfo `json:"serviceInfo" mapstructure:"serviceInfo"`
	StageTest      interface{}   `json:"stageTest" mapstructure:"stageTest"`
	PlayType       int           `json:"playType" mapstructure:"playType"`
	HasShare       int           `json:"hasShare" mapstructure:"hasShare"`
	IndexLessonId  int           `json:"indexLessonId" mapstructure:"indexLessonId"`
	Mode           int           `json:"mode" mapstructure:"mode"`
	BanxueInfo     []BanxueInfo  `json:"banxueInfo" mapstructure:"banxueInfo"`
}

type BanxueInfo struct {
	BanxueStatus int          `json:"banxueStatus" mapstructure:"banxueStatus"`
	BanxueTime   []BanxueTime `json:"banxueTime" mapstructure:"banxueTime"`
}

type BanxueTime struct {
	StartTime int64 `json:"startTime" mapstructure:"startTime"`
	EndTime   int64 `json:"endTime" mapstructure:"endTime"`
}

// 判断一个章节是否有小英服务, 是跟课的逻辑, 非dal逻辑
func (l *LessonInfo) HasLittleEnglishService() bool {
	for _, ser := range l.ServiceInfo {
		if ser.ServiceId == LittleEnglishLiveRoomServiceId {
			return true
		}
	}

	return false
}

// 判断一个章节是否课中出镜
func (l *LessonInfo) HasMidClassService() bool {
	for _, ser := range l.ServiceInfo {
		if ser.ServiceId == ServiceIdMidClass {
			return true
		}
	}

	return false
}

// 解码兼容传回章节对应空数组的情况，如：lessonId:[]
func getKVByLessonId(ctx *gin.Context, lessonIds []int, lessonFields []string) (map[string]LessonInfo, error) {
	app := conf.GetAppName()
	arrParams := map[string]interface{}{
		"lessonIds":    lessonIds,
		"lessonFields": lessonFields,
	}
	var output map[string]map[string]interface{}

	if util.GrayConfig.IsGrayDalGoApiSwitch(ctx) == true { //命中灰度走dalgo接口
		arrHeader := zbcore.GetHeaders(dalGetLessonApi, LessonModule, LessonEntity, "getKV", false, app)
		apiResp, err := zbcore.PostDalGo(ctx, arrParams, arrHeader, &output)
		zlog.Debugf(ctx, "PostDalGo apiResp:%+v", apiResp)
		if err != nil {
			zlog.Errorf(ctx, "dalgo api error, err:%s", err.Error())
			return nil, err
		}
	} else {
		arrHeader := zbcore.GetHeaders(zbcore.ServiceUri, LessonModule, LessonEntity, "getKV", false, app)
		apiResp, err := zbcore.PostDal(ctx, arrParams, arrHeader, &output)
		zlog.Debugf(ctx, "PostDal apiResp:%+v", apiResp)
		if err != nil {
			zlog.Errorf(ctx, "dal api error, err:%s", err.Error())
			return nil, err
		}
	}

	result := decodeLessonResponse(ctx, output)
	return result, nil
}

func decodeLessonResponse(ctx *gin.Context, data map[string]map[string]interface{}) map[string]LessonInfo {
	result := make(map[string]LessonInfo)
	for lessonIdStr, lessonMap := range data {
		if len(lessonMap) == 0 {
			zlog.Warnf(ctx, "lessonMap is empty, lessonId:%s", lessonIdStr)
			continue
		}

		var lesson = LessonInfo{}
		if err := api.DecodeInterface(ctx, lessonMap, &lesson); err != nil {
			zlog.Warnf(ctx, "decodeInterface failed, lessonId:%s, lessonMap:%+v, err:%s", lessonIdStr, lessonMap, err)
			continue
		}

		result[lessonIdStr] = lesson
	}

	return result
}

func getLessonAllFields() []string {
	return []string{
		"lessonId",
		"courseId",       // 课程获取int | 章节获取 array [55424,33152]
		"lessonName",     // 章节名称
		"startTime",      // 规定章节开始时间
		"stopTime",       // 规定章节结束时间
		"status",         // 章节状态 Zb_Const_Lesson 枚举值
		"lessonType",     // 章节类型,班课只有两种类型[1:主题课 ,2:提升课]
		"hasHomework",    // 是否有作业(测试系统汇聚) //此字段2020-11-15之后测试系统将不再维护，请以Api_Examcore::getRelation接口数据为准
		"hasPlayback",    // 是否有回放(回放系统汇聚)
		"finishTime",     // 老师关播时间,实际结束时间(下课命令)
		"previewNoteUri", // 课前预习,学习资料
		"classNoteUri",   // 课堂笔记
		"reopenLessonId", // 重开章节
		"fileList",       // 重开章节
		"outlineId",      // 大纲ID
		"reopenLessonId", // 重开章节
		"serviceInfo",    // 服务字段 ！！！注意：该字段只有通过章节Id查询才会得到，getLessonBaseByLessonIds
		"stageTest",      // 阶段测试
		"playType",       // 章节类型
		"shareIdList",    // 共享章节ID列表 没有共享章节时，该字段为空；是共享章节时，章节列表包含当前章节
		"hasShare",       // 是否共享
		"indexLessonId",  // 共享章节id
		"banxueInfo",     // 伴学信息
	}
}

// 批量章节获取章节信息
func GetLessonBaseByLessonIds(ctx *gin.Context, lessonIds []int, fields []string) (map[int]*LessonInfo, error) {
	if len(lessonIds) == 0 {
		return nil, components.ErrorParamInvalid
	}

	if len(fields) == 0 {
		fields = getLessonAllFields()
	}

	ret, err := getKVByLessonId(ctx, lessonIds, fields)
	if err != nil {
		//重试一次
		ret, err = getKVByLessonId(ctx, lessonIds, fields)
	}
	if err != nil {
		return nil, err
	}

	LessonInfoMap := map[int]*LessonInfo{}
	for _, lessonInfo := range ret {
		LessonInfoMap[lessonInfo.LessonId] = &lessonInfo
	}

	return LessonInfoMap, nil
}

func GetLessonBaseByLessonId(ctx *gin.Context, lessonId int, fields []string) (*LessonInfo, error) {
	if lessonId == 0 {
		return nil, components.ErrorParamInvalid
	}

	lessonIds := []int{
		lessonId,
	}
	if len(fields) == 0 {
		fields = getLessonAllFields()
	}

	ret, err := getKVByLessonId(ctx, lessonIds, fields)
	if err != nil {
		//重试一次
		ret, err = getKVByLessonId(ctx, lessonIds, fields)
	}
	if err != nil {
		return nil, err
	}

	if lesson, ok := ret[strconv.Itoa(lessonId)]; ok {
		return &lesson, nil
	}

	return nil, errors.New("lesson not found")
}
