package jxexamui

// ExamUIResponse jxexamui API通用响应结构
type ExamUIResponse struct {
	ErrNo  int                    `json:"errNo"`
	ErrMsg string                 `json:"errMsg"`
	Data   map[int64]ExamTypeInfo `json:"data"`
}

// ExamUILevelResponse 获取结果等级的响应结构
type ExamUILevelResponse struct {
	ErrNo  int           `json:"errNo"`
	ErrMsg string        `json:"errMsg"`
	Data   map[int64]int `json:"data"`
}

// PreviewInfoResponse 预习信息响应结构
type PreviewInfoResponse struct {
	ErrNo  int                   `json:"errNo"`
	ErrMsg string                `json:"errMsg"`
	Data   map[int64]PreviewInfo `json:"data"`
}

// HomeworkOpenTimeResponse 作业开启时间响应结构
type HomeworkOpenTimeResponse struct {
	ErrNo  int                        `json:"errNo"`
	ErrMsg string                     `json:"errMsg"`
	Data   map[int64]HomeworkOpenInfo `json:"data"`
}

// ExamTypeInfo 章节试卷类型信息
type ExamTypeInfo struct {
	ILabLesson bool `json:"iLabLesson"` // 是否为iLab课程
	Version    int  `json:"version"`    // 版本号
}

// PreviewInfo 预习信息
type PreviewInfo struct {
	Status        int `json:"status"`        // 是否有预习 0没有 1有
	IsOpen        int `json:"isOpen"`        // 是否开启 0未开启 1开启
	PreviewStatus int `json:"previewStatus"` // 预习类型 1.pdf 2.习题 3.都有
}

// HomeworkOpenInfo 作业开启信息
type HomeworkOpenInfo struct {
	IsOpen int `json:"isOpen"` // 是否开启 0未开启 1开启
}

// iLab等级映射，对应PHP中的Api_ExamUI::$levelIlabMap
var LevelIlabMap = map[int]string{
	0: "",
	1: "优秀",
	2: "良好",
	3: "待提升",
}

// 试卷绑定类型常量，对应PHP中的Api_Exam::BIND_TYPE_*
const (
	BindTypePreview         = 5  // 课前预习 小学预习
	BindTypePosttestMore    = 13 // 初高中预习测试
	BindTypeHomeworkIlab    = 11 // iLab巩固练习
	BindTypeHomework        = 7  // 普通巩固练习
	BindTypeTestInClass     = 10 // 堂堂测
	BindTypePracticeInClass = 1  // 课中练习
)
