package jxexamui

import (
	"deskcrm/api"
	"deskcrm/conf"
	"deskcrm/libs/utils"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// Client jxexamui API客户端
type Client struct {
	cli *base.ApiClient
}

// NewClient create Client instance
func NewClient() *Client {
	c := &Client{
		cli: conf.API.JxExamUI,
	}
	return c
}

const (
	getExamTypeApi         = "/jxexamui/middle/mt/getexamtype"             // 获取章节试卷类型
	getResultListApi       = "/jxexamui/middle/mt/getresultlist"           // 获取学生章节结果等级
	getLessonPreviewApi    = "/jxexamui/middle/preview/getpreview"         // 获取章节预习信息
	getHomeworkOpenTimeApi = "/jxexamui/middle/homework/getexamanswertime" // 获取巩固练习开启时间
)

// GetExamTypeByLessonIds 判断章节是否为ilab类型的巩固练习、是否绑定了目标
// 对应PHP中的Api_ExamUI::getExamTypeByLessonIds方法
func (c *Client) GetExamTypeByLessonIds(ctx *gin.Context, lessonIds []int64) (map[int64]ExamTypeInfo, error) {
	if len(lessonIds) == 0 {
		return make(map[int64]ExamTypeInfo), nil
	}

	// 构建请求参数
	req := map[string]interface{}{
		"lessonIds": lessonIds,
	}

	// 设置请求选项
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeJson}
	utils.DecorateHttpOptions(ctx, &opts)

	// 发送请求
	res, err := c.cli.HttpPost(ctx, getExamTypeApi, opts)
	if err != nil {
		zlog.Warnf(ctx, "JxExamUI GetExamTypeByLessonIds request err: %v", err)
		return nil, err
	}

	// 检查 HTTP 状态码
	if err = api.ApiHttpCode(ctx, res); err != nil {
		return nil, err
	}

	// 解析响应
	var examUIResp ExamUIResponse
	if _, err = api.DecodeResponse(ctx, res, &examUIResp); err != nil {
		return nil, err
	}

	// 转换为map格式
	result := make(map[int64]ExamTypeInfo)
	for lessonId, info := range examUIResp.Data {
		result[lessonId] = info
	}

	return result, nil
}

// GetLevelByExamTypeUidLessonIds 根据测试类型获取单学生多章节的结果等级
// 对应PHP中的Api_ExamUI::getLevelByExamTypeUidLessonIds方法
func (c *Client) GetLevelByExamTypeUidLessonIds(ctx *gin.Context, studentUid int64, examTypes []int, lessonIds []int64) (map[int]map[int64]int, error) {
	if studentUid == 0 || len(examTypes) == 0 || len(lessonIds) == 0 {
		return make(map[int]map[int64]int), nil
	}

	result := make(map[int]map[int64]int)

	// 为每个examType发送请求
	for _, examType := range examTypes {
		req := map[string]interface{}{
			"uid":       studentUid,
			"lessonIds": lessonIds,
			"examType":  examType,
		}

		opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeJson}
		utils.DecorateHttpOptions(ctx, &opts)

		res, err := c.cli.HttpPost(ctx, getResultListApi, opts)
		if err != nil {
			zlog.Warnf(ctx, "JxExamUI GetLevelByExamTypeUidLessonIds request err: %v", err)
			continue
		}

		if err = api.ApiHttpCode(ctx, res); err != nil {
			continue
		}

		var examUIResp ExamUILevelResponse
		if _, err = api.DecodeResponse(ctx, res, &examUIResp); err != nil {
			continue
		}

		// 转换为map格式
		levelMap := make(map[int64]int)
		for lessonId, level := range examUIResp.Data {
			levelMap[lessonId] = level
		}
		result[examType] = levelMap
	}

	return result, nil
}

// GetLessonPreviewInfo 获取多章节预习类型（PDF、题目、PDF+题目）、预习是否开启
// 对应PHP中的Api_Inclass::getLessonPreviewInfo方法
func (c *Client) GetLessonPreviewInfo(ctx *gin.Context, lessonIds []int64) (map[int64]PreviewInfo, error) {
	if len(lessonIds) == 0 {
		return make(map[int64]PreviewInfo), nil
	}

	// 构建请求参数
	req := map[string]interface{}{
		"lessonIds": lessonIds,
		"source":    "wx",
	}

	// 设置请求选项
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeJson}
	utils.DecorateHttpOptions(ctx, &opts)

	// 发送请求
	res, err := c.cli.HttpPost(ctx, getLessonPreviewApi, opts)
	if err != nil {
		zlog.Warnf(ctx, "JxExamUI GetLessonPreviewInfo request err: %v", err)
		return nil, err
	}

	// 检查 HTTP 状态码
	if err = api.ApiHttpCode(ctx, res); err != nil {
		return nil, err
	}

	// 解析响应
	var examUIResp PreviewInfoResponse
	if _, err = api.DecodeResponse(ctx, res, &examUIResp); err != nil {
		return nil, err
	}

	// 转换为map格式
	result := make(map[int64]PreviewInfo)
	for lessonId, info := range examUIResp.Data {
		result[lessonId] = info
	}

	return result, nil
}

// GetHomeworkOpenTime 获取巩固练习开启时间
// 对应PHP中的Api_Exam::getHomeworkOpenTimeByCourseIds方法
func (c *Client) GetHomeworkOpenTime(ctx *gin.Context, courseLessonList map[int64]map[int64]map[string]int64) (map[int64]HomeworkOpenInfo, error) {
	if len(courseLessonList) == 0 {
		return make(map[int64]HomeworkOpenInfo), nil
	}

	// 构建请求参数
	req := map[string]interface{}{
		"courseIds": courseLessonList,
	}

	// 设置请求选项
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeJson}
	utils.DecorateHttpOptions(ctx, &opts)

	// 发送请求
	res, err := c.cli.HttpPost(ctx, getHomeworkOpenTimeApi, opts)
	if err != nil {
		zlog.Warnf(ctx, "JxExamUI GetHomeworkOpenTime request err: %v", err)
		return nil, err
	}

	// 检查 HTTP 状态码
	if err = api.ApiHttpCode(ctx, res); err != nil {
		return nil, err
	}

	// 解析响应
	var examUIResp HomeworkOpenTimeResponse
	if _, err = api.DecodeResponse(ctx, res, &examUIResp); err != nil {
		return nil, err
	}

	// 转换为map格式
	result := make(map[int64]HomeworkOpenInfo)
	for lessonId, info := range examUIResp.Data {
		result[lessonId] = info
	}

	return result, nil
}
