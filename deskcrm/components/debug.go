package components

import (
	"deskcrm/libs/json"

	json2 "deskcrm/libs/json"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"reflect"
)

func IsDebug(ctx *gin.Context) bool {
	isDebug, exists := ctx.Get("isDebug")
	if exists {
		return cast.ToBool(isDebug)
	}
	return false
}

func Debugf(ctx *gin.Context, format string, args ...interface{}) {
	traceLog, ok := ctx.Get("isDebug")
	if ok && traceLog == true {
		//非基础类型的数据,自动转json
		for idx, arg := range args {
			argType := reflect.TypeOf(arg).Kind()
			if argType == reflect.Struct || argType == reflect.Array || argType == reflect.Slice || argType == reflect.Map || argType == reflect.Ptr {
				argString, _ := json2.MarshalToString(arg)
				args[idx] = argString
			}
		}

		//ctx 中带 traceLog 标的
		zlog.Infof(ctx, format, args...)
		return
	}
	return
}

// DebugfWithJSON 使用JSON格式打印调试日志
// msg: 日志消息，格式为 "方法名 调用方法名, data: %s"
// data: 要序列化为JSON的数据
func DebugfWithJSON(ctx *gin.Context, msg string, data interface{}) {
	if dataJson, _ := json.MarshalToString(data); dataJson != "" {
		Debugf(ctx, msg, dataJson)
	}
}

// DebugfWithJSONAndCount 使用JSON格式打印调试日志（包含数量）
// msg: 日志消息，格式为 "方法名 调用方法名 total: %d, data: %s"
// count: 数据数量
// data: 要序列化为JSON的数据
func DebugfWithJSONAndCount(ctx *gin.Context, msg string, count int, data interface{}) {
	if dataJson, _ := json.MarshalToString(data); dataJson != "" {
		Debugf(ctx, msg, count, dataJson)
	}
}
