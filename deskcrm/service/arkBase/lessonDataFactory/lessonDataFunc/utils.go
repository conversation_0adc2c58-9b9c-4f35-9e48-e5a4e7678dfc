package lessonDataFunc

import "fmt"

// formatDuration 格式化时长为"XminYs"格式
// 对应PHP中的AssistantDesk_Tools::formatDurationTime方法
func FormatDuration(seconds int64) string {
	remainingSeconds := seconds % 60
	minutes := seconds / 60
	retTime := fmt.Sprintf("%dmin", minutes)
	if remainingSeconds > 0 {
		retTime = retTime + fmt.Sprintf("%ds", remainingSeconds)
	}
	return retTime
}

// getHomeworkLevelString 根据作业等级数值返回对应的字符串
func GetHomeworkLevelString(level int64) string {
	// 对应PHP中的 Api_Das::$homeworkLevelMap
	switch level {
	case 1:
		return "A"
	case 2:
		return "B"
	case 3:
		return "C"
	case 4:
		return "D"
	case 5:
		return "E"
	default:
		return "暂无等级"
	}
}

// LessonDataArray 创建新的课程数据数组，对应PHP中的数组格式 [显示文本, 颜色, 是否可点击]
// 例如：$row['preview'] = ['-', 'gray', 1]
func LessonDataArray(text, color string, clickable int) []interface{} {
	return []interface{}{text, color, clickable}
}
