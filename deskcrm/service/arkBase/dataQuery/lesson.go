package dataQuery

import (
	"deskcrm/api/assistantdeskgo"
	"deskcrm/api/dataproxy"
	"encoding/json"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// GetLuData 获取课程学生的LU数据
func (s *Singleton) GetLuData(ctx *gin.Context, courseId int64, studentUid int64, fields []string) (map[int64]*dataproxy.GetStudentLessonDataCommonResp, error) {
	if courseId == 0 || studentUid == 0 || len(fields) == 0 {
		return make(map[int64]*dataproxy.GetStudentLessonDataCommonResp), nil
	}

	client := dataproxy.NewClient()
	params := dataproxy.GetListByCourseIdsStudentUidsParam{
		CourseIds:   []int64{courseId},
		StudentUids: []int64{studentUid},
		Fields:      fields,
	}

	data, err := client.GetListByCourseIdsStudentUids(ctx, params)
	if err != nil {
		zlog.Warnf(ctx, "GetCommonLuDataByLessonStudents failed: %v", err)
		return nil, err
	}

	result := make(map[int64]*dataproxy.GetStudentLessonDataCommonResp)
	for _, item := range data {
		result[item.LessonId] = item
	}

	return result, nil
}

// GetLuData 获取课程学生的公共LU数据
func (s *Singleton) GetCommonLuData(ctx *gin.Context, lessonIds []int64, studentUid int64, fields []string) (map[int64]*dataproxy.GetCommonLuResp, error) {
	if len(lessonIds) == 0 || studentUid == 0 || len(fields) == 0 {
		return make(map[int64]*dataproxy.GetCommonLuResp), nil
	}

	client := dataproxy.NewClient()
	params := dataproxy.GetLuComonListByStudentLessonsParam{
		LessonIds:  lessonIds,
		StudentUid: studentUid,
		Fields:     fields,
	}

	data, err := client.GetLuComonListByStudentLessons(ctx, params)
	if err != nil {
		zlog.Warnf(ctx, "GetCommonLuDataByLessonStudents failed: %v", err)
		return nil, err
	}

	result := make(map[int64]*dataproxy.GetCommonLuResp)
	for _, item := range data {
		result[item.LessonId] = item
	}

	return result, nil
}

// GetListByCourseIdsStudentUids 获取学生课程数据（包含回放数据）
func (s *Singleton) GetListByCourseIdsStudentUids(ctx *gin.Context, courseIds []int64, studentUids []int64, fields []string) ([]*dataproxy.GetStudentLessonDataCommonResp, error) {
	return dataproxy.NewClient().GetListByCourseIdsStudentUids(ctx, dataproxy.GetListByCourseIdsStudentUidsParam{
		CourseIds:   courseIds,
		StudentUids: studentUids,
		Fields:      fields,
	})
}

// GetLuDataRaw 获取课程学生的LU数据（原始map格式，支持动态字段）
// 暂时使用现有的GetLuData方法，然后转换为map格式
// TODO: 实现真正的原始数据获取方法
func (s *Singleton) GetLuDataRaw(ctx *gin.Context, courseId int64, studentUid int64, fields []string) (map[int64]map[string]interface{}, error) {
	if courseId == 0 || studentUid == 0 || len(fields) == 0 {
		return make(map[int64]map[string]interface{}), nil
	}

	// 暂时使用现有的GetLuData方法
	structData, err := s.GetLuData(ctx, courseId, studentUid, fields)
	if err != nil {
		return nil, err
	}

	// 转换为map格式
	result := make(map[int64]map[string]interface{})
	for lessonId, data := range structData {
		// 将结构体转换为map
		dataBytes, err := json.Marshal(data)
		if err != nil {
			zlog.Warnf(ctx, "GetLuDataRaw marshal failed: %v", err)
			continue
		}

		var dataMap map[string]interface{}
		if err := json.Unmarshal(dataBytes, &dataMap); err != nil {
			zlog.Warnf(ctx, "GetLuDataRaw unmarshal failed: %v", err)
			continue
		}

		// 手动添加点分隔的字段（模拟PHP的行为）
		// 注意：这里需要根据实际的数据结构来设置这些字段
		// 暂时使用默认值，实际使用时需要从真正的数据源获取
		dataMap["exam_answer.exam33.correct_level"] = 0
		dataMap["exam_answer.exam33.last_correct_status"] = 0

		result[lessonId] = dataMap
	}

	return result, nil
}

// GetExamRelationData 获取试卷绑定数据
func (s *Singleton) GetExamRelationData(ctx *gin.Context, bindIds []int64, bindType int64, relationTypes []int, fields []string) (map[int64]interface{}, error) {
	if len(bindIds) == 0 || bindType == 0 || len(fields) == 0 {
		return make(map[int64]interface{}), nil
	}

	client := dataproxy.NewClient()

	params := dataproxy.GetListByBindIdsBindTypeRelationTypesCommonParam{
		BindIds:       bindIds,
		BindType:      bindType,
		RelationTypes: relationTypes,
		Fields:        fields,
	}

	data, err := client.GetListByBindIdsBindTypeRelationTypes(ctx, params)
	if err != nil {
		zlog.Warnf(ctx, "GetExamRelationData failed: %v", err)
		return nil, err
	}

	result := make(map[int64]interface{})
	for _, item := range data {
		result[item.BindId] = item
	}

	return result, nil
}

func (s *Singleton) GetLearnReportByClueGradeIds(ctx *gin.Context, clueIdGradeIdMap map[string]int64) (getLessonListResp map[string]assistantdeskgo.LearnReportInfo, err error) {
	leadsInfoMap, err := assistantdeskgo.NewClient().GetLearnReportByClueGradeIds(ctx, clueIdGradeIdMap)
	if err != nil {
		zlog.Warnf(ctx, "GetPrivateLeadsData failed: %s", err.Error())
		return leadsInfoMap, nil
	}
	return leadsInfoMap, nil
}

// GetLessonDataByLessonIds 获取章节数据（包含mix_interaction_total_num字段）
func (s *Singleton) GetLessonDataByLessonIds(ctx *gin.Context, lessonIds []int64, fields []string) (map[int64]*dataproxy.GetLessonDataByLessonIdsResp, error) {
	if len(lessonIds) == 0 {
		return make(map[int64]*dataproxy.GetLessonDataByLessonIdsResp), nil
	}

	data, err := dataproxy.NewClient().GetLessonDataByLessonIds(ctx, dataproxy.GetLessonDataByLessonIdsParam{
		LessonIds: lessonIds,
		Fields:    fields,
	})
	if err != nil {
		return nil, err
	}

	result := make(map[int64]*dataproxy.GetLessonDataByLessonIdsResp)
	for _, item := range data {
		result[item.LessonId] = item
	}

	return result, nil
}
