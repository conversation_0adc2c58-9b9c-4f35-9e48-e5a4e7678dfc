package dataQuery

import (
	"deskcrm/models"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// GetLessonStudentData 获取学生章节请假信息
func (s *Singleton) GetLessonStudentData(ctx *gin.Context, courseId int64, studentUid int64) (map[int64]*models.LessonStudent, error) {
	if studentUid == 0 || courseId == 0 {
		return make(map[int64]*models.LessonStudent), nil
	}

	data, err := models.LessonStudentDao.GetListByLessonIds(ctx, courseId, studentUid)
	if err != nil {
		zlog.Warnf(ctx, "GetLessonStudentData failed: %v", err)
		return nil, err
	}

	// 构建映射表 key: "lessonId_studentUid"
	resultMap := make(map[int64]*models.LessonStudent)
	for _, lessonStudent := range data {
		resultMap[lessonStudent.LessonId] = &lessonStudent
	}

	return resultMap, nil
}
