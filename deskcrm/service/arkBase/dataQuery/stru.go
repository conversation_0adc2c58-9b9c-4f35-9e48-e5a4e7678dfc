package dataQuery

import (
	"deskcrm/api/jxexamui"
	"deskcrm/models"
)

const (
	// 用于格式化练习数据：正确数|参与数|总数
	LESSON_EXERCISE_DETAIL = "%d|%d|%d"
)

type CustomFieldData struct {
	FieldInfos []*models.CustomField
	CustomData []*models.StudentCourseFieldData
}

// ILabInfo iLab课程信息
type ILabInfo struct {
	PreviewInfoByIlab     map[int64]int                   // 预习信息(按章节ID)
	HomeworkInfoByIlab    map[int64]int                   // iLab巩固练习信息(按章节ID)
	InclassTestInfoByIlab map[int64]int                   // 堂堂测信息(按章节ID)
	HomeworkInfoByNomal   map[int64]int                   // 普通巩固练习信息(按章节ID)
	CheckIlabLesson       map[int64]jxexamui.ExamTypeInfo // 章节iLab检查信息(按章节ID)
}

// PreviewOpenInfo 预习开启状态信息
type PreviewOpenInfo struct {
	HasPreview      int // 是否有预习 0没有 1有
	IsOpenPreview   int // 是否开启 0未开启 1开启
	IsPreviewFinish int // 是否完成 0未完成 1已完成
}
