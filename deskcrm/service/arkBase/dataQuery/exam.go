package dataQuery

import (
	"deskcrm/api/assistantdesk"
	"deskcrm/api/dataproxy"
	"deskcrm/api/examcore"
	"deskcrm/api/frontcourse"
	"deskcrm/api/jxexamui"
	"deskcrm/components/define"
	"deskcrm/consts"
	"fmt"
	"slices"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func (s *Singleton) GetInteractTotalNum(ctx *gin.Context, lessonIds []int64) (map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp, error) {

	detailRsp, err := dataproxy.NewClient().GetListByBindIdsBindTypeRelationTypesExamTags(ctx, dataproxy.GetListByBindIdsBindTypeRelationTypesCommonParam{
		BindType:      RelationTypeLesson,
		BindIds:       lessonIds,
		RelationTypes: getTotalNumExamTypes,
		ExamTags:      []int64{0},
		Fields:        []string{"bindId", "relationType", "totalNum", "bindStatus"},
	})
	if err != nil {
		return nil, err
	}

	resMap := make(map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp)
	for _, item := range detailRsp {
		temp := item

		resMap[temp.BindId] = temp
	}

	return resMap, nil
}

// GetExamTotalNum 获取考试总数信息
// 对应PHP中的examTotalNum数据，按章节和考试类型组织
func (s *Singleton) GetExamTotalNum(ctx *gin.Context, lessonIds []int64) (map[int64]map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp, error) {
	if len(lessonIds) == 0 {
		return make(map[int64]map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp), nil
	}

	// 定义需要获取的考试类型
	examTypes := []int64{
		consts.BindTypePracticeInClass,    // 1 - 互动题
		consts.BindTypeTestInClass,        // 10 - 堂堂测
		consts.BindTypePrimaryMathPreview, // 11 - 同步练习
		consts.BindTypeOralQuestion,       // 口述题
		consts.BindTypeHomework,           // 7 - 巩固练习
		consts.BindTypePreview,            // 5 - 预习
		consts.BindTypePosttestMore,       // 13 - 初高预习
	}

	detailRsp, err := dataproxy.NewClient().GetListByBindIdsBindTypeRelationTypesExamTags(ctx, dataproxy.GetListByBindIdsBindTypeRelationTypesCommonParam{
		BindType:      0, // 章节绑定
		BindIds:       lessonIds,
		RelationTypes: examTypes,
		ExamTags:      []int64{0},
		Fields:        []string{"bindId", "relationType", "totalNum", "bindStatus"},
	})
	if err != nil {
		return nil, err
	}

	// 按章节ID和考试类型组织数据
	resMap := make(map[int64]map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp)
	for _, item := range detailRsp {
		temp := item
		if resMap[temp.BindId] == nil {
			resMap[temp.BindId] = make(map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp)
		}
		resMap[temp.BindId][temp.RelationType] = temp
	}

	return resMap, nil
}

func (s *Singleton) GetExamBindData(ctx *gin.Context, lessonId int64, bindType int) (assistantdesk.ExamBindRes, error) {

	detailRsp, err := assistantdesk.NewClient().GetExamBindStatus(ctx, assistantdesk.GetExamBindStatusParams{
		LessonId: lessonId,
		BindType: bindType,
	})
	if err != nil {
		return detailRsp, err
	}

	return detailRsp, nil
}

// GetILabInfo 获取iLab课程信息
// 对应PHP中的initILabInfo方法
func (s *Singleton) GetILabInfo(ctx *gin.Context, gradeId, subjectId int, lessonIds []int64, studentUid int64) (*ILabInfo, error) {
	result := &ILabInfo{
		PreviewInfoByIlab:     make(map[int64]int),
		HomeworkInfoByIlab:    make(map[int64]int),
		InclassTestInfoByIlab: make(map[int64]int),
		HomeworkInfoByNomal:   make(map[int64]int),
		CheckIlabLesson:       make(map[int64]jxexamui.ExamTypeInfo),
	}

	// 只处理初二物理课程
	acceptGrade := []int{consts.ILabGradeId, 4}  // 初二、初三
	acceptSubject := []int{consts.ILabSubjectId} // 物理学科ID

	gradeMatch := slices.Contains(acceptGrade, gradeId)
	subjectMatch := slices.Contains(acceptSubject, subjectId)
	if !gradeMatch || !subjectMatch {
		return result, nil
	}

	// 获取章节试卷类型信息
	checkIlabLesson, err := jxexamui.NewClient().GetExamTypeByLessonIds(ctx, lessonIds)
	if err != nil {
		zlog.Warnf(ctx, "GetILabInfo GetExamTypeByLessonIds failed: %v", err)
		return result, nil
	}
	result.CheckIlabLesson = checkIlabLesson

	if len(checkIlabLesson) == 0 {
		return result, nil
	}

	// 获取学生各类型试卷的结果等级
	examTypes := []int{
		jxexamui.BindTypeHomeworkIlab, // iLab巩固练习
		jxexamui.BindTypePosttestMore, // 初高中预习测试
		jxexamui.BindTypeTestInClass,  // 堂堂测
		jxexamui.BindTypeHomework,     // 普通巩固练习
	}

	ilabRet, err := jxexamui.NewClient().GetLevelByExamTypeUidLessonIds(ctx, studentUid, examTypes, lessonIds)
	if err != nil {
		zlog.Warnf(ctx, "GetILabInfo GetLevelByExamTypeUidLessonIds failed: %v", err)
		return result, nil
	}

	// 填充结果
	if levels, ok := ilabRet[jxexamui.BindTypeHomeworkIlab]; ok {
		result.HomeworkInfoByIlab = levels
	}
	if levels, ok := ilabRet[jxexamui.BindTypePosttestMore]; ok {
		result.PreviewInfoByIlab = levels
	}
	if levels, ok := ilabRet[jxexamui.BindTypeTestInClass]; ok {
		result.InclassTestInfoByIlab = levels
	}
	if levels, ok := ilabRet[jxexamui.BindTypeHomework]; ok {
		result.HomeworkInfoByNomal = levels
	}

	return result, nil
}

// GetPreviewOpenInfo 获取预习开启状态信息
// 对应PHP中的initPreviewIsOpenInfos方法
func (s *Singleton) GetPreviewOpenInfo(ctx *gin.Context, courseId int64, lessonIds []int64, lessonList map[int64]interface{}, gradeStage int) (map[int64]PreviewOpenInfo, error) {
	result := make(map[int64]PreviewOpenInfo)

	if len(lessonIds) == 0 {
		return result, nil
	}

	now := time.Now().Unix()

	switch gradeStage {
	case define.GradeStagePrimary: // 小学和低幼学部
		// 小学预习情况 - 通过试卷绑定关系判断
		bindList := make([]string, 0, len(lessonIds))
		for _, lessonId := range lessonIds {
			bindKey := fmt.Sprintf("lesson_%d:%d", lessonId, jxexamui.BindTypePreview)
			bindList = append(bindList, bindKey)
		}

		examRelationList, err := examcore.NewClient().GetRelation(ctx, bindList)
		if err != nil {
			zlog.Warnf(ctx, "GetPreviewOpenInfo GetRelation failed: %v", err)
			examRelationList = make(map[string]map[int64]examcore.BindInfo)
		}

		for _, lessonId := range lessonIds {
			bindKey := fmt.Sprintf("lesson_%d:%d", lessonId, jxexamui.BindTypePreview)
			examRelationDetail := examRelationList[bindKey]

			hasPreview := 0
			if len(examRelationDetail) > 0 {
				hasPreview = 1
			}

			isOpenPreview := 0
			if hasPreview == 1 {
				// 获取章节开始时间，判断是否在开课7天内
				if lessonInfo, ok := lessonList[lessonId]; ok {
					if lessonMap, ok := lessonInfo.(map[string]interface{}); ok {
						if startTimeInterface, ok := lessonMap["startTime"]; ok {
							if startTime, ok := startTimeInterface.(int64); ok {
								// 开课7天前开启预习
								sevenDaysBeforeStart := startTime - (7 * 24 * 3600)
								if now > sevenDaysBeforeStart {
									isOpenPreview = 1
								}
							}
						}
					}
				}
			}

			result[lessonId] = PreviewOpenInfo{
				HasPreview:    hasPreview,
				IsOpenPreview: isOpenPreview,
			}
		}

	case define.GradeStageJunior, define.GradeStageSenior: // 初高中
		// 初高中预习情况 - 通过frontcourse接口获取
		apiData, err := frontcourse.NewClient().GetHighGradePreviewInfo(ctx, lessonIds)
		if err != nil {
			zlog.Warnf(ctx, "GetPreviewOpenInfo GetHighGradePreviewInfo failed: %v", err)
			apiData = make(map[int64]frontcourse.HighGradePreviewInfo)
		}

		for _, lessonId := range lessonIds {
			hasPreview := 0
			isOpenPreview := 0

			if info, ok := apiData[lessonId]; ok {
				if info.Status > 0 {
					hasPreview = 1
				}
				isOpenPreview = info.IsOpen
			}

			result[lessonId] = PreviewOpenInfo{
				HasPreview:    hasPreview,
				IsOpenPreview: isOpenPreview,
			}
		}

	default:
		// 其他学段暂不处理
		for _, lessonId := range lessonIds {
			result[lessonId] = PreviewOpenInfo{
				HasPreview:    0,
				IsOpenPreview: 0,
			}
		}
	}

	return result, nil
}

// GetHomeworkOpenInfo 获取作业开启信息
// 对应PHP中的initHomeworkIsOpenInfos方法
func (s *Singleton) GetHomeworkOpenInfo(ctx *gin.Context, courseId int64, lessonList map[int64]interface{}) (map[int64]jxexamui.HomeworkOpenInfo, error) {
	result := make(map[int64]jxexamui.HomeworkOpenInfo)

	if courseId == 0 || len(lessonList) == 0 {
		return result, nil
	}

	// 构建课程章节列表
	courseLessonList := make(map[int64]map[int64]map[string]int64)
	courseLessonList[courseId] = make(map[int64]map[string]int64)

	for lessonId, lessonInfo := range lessonList {
		if lessonMap, ok := lessonInfo.(map[string]interface{}); ok {
			if stopTimeInterface, ok := lessonMap["stopTime"]; ok {
				if stopTime, ok := stopTimeInterface.(int64); ok {
					courseLessonList[courseId][lessonId] = map[string]int64{
						"stopTime": stopTime,
					}
				}
			}
		}
	}

	// 调用jxexamui接口获取作业开启信息
	apiData, err := jxexamui.NewClient().GetHomeworkOpenTime(ctx, courseLessonList)
	if err != nil {
		zlog.Warnf(ctx, "GetHomeworkOpenInfo failed: %v", err)
		return result, nil
	}

	return apiData, nil
}

// GetHomeworkBindExams 获取章节作业绑定情况
// 对应PHP中的AssistantDesk_ExamBind::lessonBindExams方法
func (s *Singleton) GetHomeworkBindExams(ctx *gin.Context, lessonIds []int64, bindType int) (map[int64]bool, error) {
	result := make(map[int64]bool)

	if len(lessonIds) == 0 {
		return result, nil
	}

	// 构建绑定字符串列表
	bindList := make([]string, 0, len(lessonIds))
	for _, lessonId := range lessonIds {
		bindStr := fmt.Sprintf("lesson_%d:%d", lessonId, bindType)
		bindList = append(bindList, bindStr)
	}

	// 获取章节试卷绑定关系
	examRelationList, err := examcore.NewClient().GetRelation(ctx, bindList)
	if err != nil {
		zlog.Warnf(ctx, "GetHomeworkBindExams GetRelation failed: %v", err)
		return result, nil
	}

	// 检查每个章节是否有绑定
	for _, lessonId := range lessonIds {
		bindStr := fmt.Sprintf("lesson_%d:%d", lessonId, bindType)
		examRelationDetail := examRelationList[bindStr]
		result[lessonId] = len(examRelationDetail) > 0
	}

	return result, nil
}

// GetLessonNeedAuditMap 获取需要审核的课程映射
// 对应PHP中的lessonNeedAuditMap检查
func (s *Singleton) GetLessonNeedAuditMap(ctx *gin.Context, lessonIds []int64) (map[int64]bool, error) {
	result := make(map[int64]bool)

	if len(lessonIds) == 0 {
		return result, nil
	}

	// TODO: 实现获取需要审核的课程映射逻辑
	// 这里需要根据具体的业务逻辑来实现
	// 暂时返回空映射，表示没有需要审核的课程

	return result, nil
}
