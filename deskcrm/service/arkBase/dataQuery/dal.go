package dataQuery

import (
	"deskcrm/api/achilles"
	"deskcrm/api/dal"
	"strconv"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

func (s *Singleton) GetCourseInfo(ctx *gin.Context, courseID int64) (res dal.CourseInfo, err error) {
	detailRsp, err := dal.GetKVByCourseId(ctx, courseID)
	if err != nil {
		return res, err
	}

	strID := strconv.Itoa(int(courseID))

	res, ok := detailRsp[strID]
	if !ok {
		zlog.Warnf(ctx, "GetCourseInfo not found this course %v", courseID)
	}

	return res, nil
}

func (s *Singleton) GetLessonInfoMap(ctx *gin.Context, courseID int64) (res map[int64]dal.LessonInfo, err error) {
	detailRsp, err := dal.GetKVByCourseId(ctx, courseID)
	if err != nil {
		return res, err
	}

	strID := strconv.Itoa(int(courseID))

	courseInfo, ok := detailRsp[strID]
	if !ok {
		zlog.Warnf(ctx, "GetLessonInfoMap not found this course %v", courseID)
	}

	res = make(map[int64]dal.LessonInfo)
	for lessonID, lessonInfo := range courseInfo.LessonList {
		res[cast.ToInt64(lessonID)] = lessonInfo
	}

	return res, nil
}

// GetLessonBaseInfo 获取章节基础信息
func (s *Singleton) GetLessonBaseInfo(ctx *gin.Context, lessonIds []int64, fields []string) (map[int64]*achilles.ProcessedLessonInfo, error) {
	if len(lessonIds) == 0 {
		return make(map[int64]*achilles.ProcessedLessonInfo), nil
	}

	// 转换为string类型（achilles API需要string类型的lessonIds）
	lessonIdStrs := make([]string, len(lessonIds))
	for i, id := range lessonIds {
		lessonIdStrs[i] = strconv.FormatInt(id, 10)
	}

	client := achilles.NewClient()
	data, err := client.GetInfoByLessonId(ctx, lessonIdStrs, fields)
	if err != nil {
		zlog.Warnf(ctx, "GetLessonBaseInfo failed: %v", err)
		return nil, err
	}

	// 转换key为int64
	result := make(map[int64]*achilles.ProcessedLessonInfo)
	for lessonIdStr, lessonInfo := range data {
		lessonId, err := strconv.ParseInt(lessonIdStr, 10, 64)
		if err != nil {
			zlog.Warnf(ctx, "Failed to parse lessonId: %s", lessonIdStr)
			continue
		}
		result[lessonId] = lessonInfo
	}

	return result, nil
}
