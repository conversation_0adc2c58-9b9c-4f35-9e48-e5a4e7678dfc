package dataQuery

import (
	"deskcrm/api/das"

	"github.com/gin-gonic/gin"
)

// GetDasLessonData 获取DAS学生章节数据
func (s *Singleton) GetDasLessonData(ctx *gin.Context, studentUids []int64, lessonID int64) (res map[int64]map[int64]*das.StudentLessonInfo, err error) {
	return das.GetDasLessonData(ctx, studentUids, lessonID, []string{})
}

// GetDasLessonsData 获取DAS学生章节数据（多个章节）
func (s *Singleton) GetDasLessonsData(ctx *gin.Context, studentUids []int64, lessonIDs []int64) (res map[int64]map[int64]*das.StudentLessonInfo, err error) {
	return das.GetDasLessonsData(ctx, studentUids, lessonIDs, []string{})
}
