package util

import (
	"deskcrm/api/mercury"
	"deskcrm/components"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"hash/crc32"
	"math/rand"
	"time"
)

type grayConfig struct {
}

var GrayConfig grayConfig

const GRAY_DEFAULT_TTL = 300
const mercuryConfigArkOptimizeRuleCacheGrayKey = "ark_optimize_rule_cache"
const mercuryConfigDalGoApiSwitchGrayKey = "fwyy_desk_dalgo_gray" //dal切dalgo接口灰度

type optimizeRuleCache struct {
	Percent int64 `json:"percent"`
}

func (s grayConfig) IsGrayOptimizeRuleConfigCache(ctx *gin.Context) (isGray bool) {
	var optimizeRuleCacheConfig optimizeRuleCache
	err := mercury.NewClient().GetConfigForJson(ctx, mercuryConfigArkOptimizeRuleCacheGrayKey, GRAY_DEFAULT_TTL, &optimizeRuleCacheConfig)
	if err != nil {
		zlog.Warnf(ctx, "HitGray GetConfigForJson fai, key=%v,err=%v", mercuryConfigArkOptimizeRuleCacheGrayKey, err)
		return false
	}

	rand.Seed(time.Now().UnixNano())
	num := rand.Int63n(100) // 生成0到99之间的随机数
	if num < optimizeRuleCacheConfig.Percent {
		return true
	}
	return false
}

type dalGoApiSwitchGrayConfigStru struct {
	Percent     int64   `json:"percent"`
	BusinessIDs []int64 `json:"businessIDs"`
	Disable     bool    `json:"disable"`
}

func (s grayConfig) IsGrayDalGoApiSwitch(ctx *gin.Context) (isGray bool) {
	if components.IsDebug(ctx) {
		return true
	}

	var dalGoApiSwitchGrayConfig dalGoApiSwitchGrayConfigStru
	err := mercury.NewClient().GetConfigForJson(ctx, mercuryConfigDalGoApiSwitchGrayKey, GRAY_DEFAULT_TTL, &dalGoApiSwitchGrayConfig)
	if err != nil {
		zlog.Warnf(ctx, "HitGray GetConfigForJson fai, key=%v,err=%v", mercuryConfigDalGoApiSwitchGrayKey, err)
		return false
	}

	if dalGoApiSwitchGrayConfig.Disable {
		return false
	}

	if dalGoApiSwitchGrayConfig.Percent >= 100 {
		return true
	}

	ipsString, _ := ctx.Cookie("ZYBIPSCAS")
	table := crc32.MakeTable(crc32.IEEE)
	num := int64(crc32.Checksum([]byte(ipsString), table)) % 100

	if num > 0 && num < dalGoApiSwitchGrayConfig.Percent {
		return true
	}
	return false
}
